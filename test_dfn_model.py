"""
Test script for the cylindrical DFN model

This script tests the basic functionality of the DFN model implementation.
"""

import numpy as np
import matplotlib.pyplot as plt
from dfn_cylindrical import CylindricalDFNModel, create_example_cell
from dfn_visualization import DFNVisualizer
from dfn_equations import DFNEquations

def test_model_initialization():
    """Test basic model initialization"""
    print("Testing model initialization...")
    
    try:
        cell_params, material_params, numerical_params = create_example_cell()
        model = CylindricalDFNModel(cell_params, material_params, numerical_params)
        
        print("✓ Model initialized successfully")
        print(f"  Grid size: {model.nr} × {model.ntheta}")
        print(f"  Cell dimensions: {model.R_inner*1000:.1f} - {model.R_outer*1000:.1f} mm")
        print(f"  Time steps: {model.time_steps}")
        
        return model
        
    except Exception as e:
        print(f"✗ Model initialization failed: {e}")
        return None

def test_electrode_regions(model):
    """Test electrode region assignment"""
    print("\nTesting electrode regions...")
    
    try:
        regions = model.get_electrode_regions()
        
        # Check region distribution
        neg_count = np.sum(regions == 0)
        sep_count = np.sum(regions == 1)
        pos_count = np.sum(regions == 2)
        total = regions.size
        
        print(f"✓ Electrode regions assigned")
        print(f"  Negative electrode: {neg_count}/{total} points ({100*neg_count/total:.1f}%)")
        print(f"  Separator: {sep_count}/{total} points ({100*sep_count/total:.1f}%)")
        print(f"  Positive electrode: {pos_count}/{total} points ({100*pos_count/total:.1f}%)")
        
        return regions
        
    except Exception as e:
        print(f"✗ Electrode region test failed: {e}")
        return None

def test_visualization(model, regions):
    """Test visualization functionality"""
    print("\nTesting visualization...")
    
    try:
        viz = DFNVisualizer(model)
        
        # Test geometry plot
        fig1 = viz.plot_cell_geometry(regions)
        fig1.suptitle("Test: Cell Geometry")
        
        # Create test data for concentration plot
        R, THETA = model.R, model.THETA
        c_e_test = (1000 + 200 * np.sin(3*THETA) * 
                   np.exp(-((R - (model.R_inner + model.R_outer)/2)**2) / (0.001)))
        
        fig2 = viz.plot_concentration_2d(c_e_test, "Test Concentration Distribution")
        
        print("✓ Visualization tests passed")
        
        return fig1, fig2
        
    except Exception as e:
        print(f"✗ Visualization test failed: {e}")
        return None, None

def test_equations(model, regions):
    """Test DFN equations module"""
    print("\nTesting DFN equations...")
    
    try:
        equations = DFNEquations(model)
        
        # Test electrolyte diffusion
        c_e_init = model.c_e.copy()
        i_e_test = np.zeros_like(model.c_e)
        
        # Simple test: uniform concentration should remain uniform
        c_e_new = equations.electrolyte_diffusion_2d(
            c_e_init, model.phi_e, i_e_test, regions, model.dt)
        
        # Check if solution is reasonable
        if np.all(np.isfinite(c_e_new)) and np.all(c_e_new > 0):
            print("✓ Electrolyte diffusion equation test passed")
        else:
            print("✗ Electrolyte diffusion equation produced invalid results")
            
        # Test Butler-Volmer kinetics
        i_bv, i_0, eta = equations.butler_volmer_kinetics(
            model.c_e, model.c_s_surf_neg, model.phi_s_neg, 
            model.phi_e, model.T, model.neg_params)
        
        if np.all(np.isfinite(i_bv)) and np.all(np.isfinite(i_0)):
            print("✓ Butler-Volmer kinetics test passed")
        else:
            print("✗ Butler-Volmer kinetics produced invalid results")
            
        return equations
        
    except Exception as e:
        print(f"✗ Equations test failed: {e}")
        return None

def test_finite_differences():
    """Test finite difference operators"""
    print("\nTesting finite difference operators...")
    
    try:
        from dfn_equations import create_finite_difference_operators_2d
        
        # Create simple test grid
        nr, ntheta = 20, 36
        dr = 0.001
        dtheta = 2*np.pi / (ntheta - 1)
        r = np.linspace(0.001, 0.01, nr)
        
        # Get operators
        radial_deriv, angular_deriv, laplacian = create_finite_difference_operators_2d(
            nr, ntheta, dr, dtheta, r)
        
        # Test with known function: f(r,θ) = r²cos(θ)
        R, THETA = np.meshgrid(r, np.linspace(0, 2*np.pi, ntheta), indexing='ij')
        f_test = R**2 * np.cos(THETA)
        
        # Analytical derivatives
        df_dr_analytical = 2 * R * np.cos(THETA)
        df_dtheta_analytical = -R**2 * np.sin(THETA)
        
        # Numerical derivatives
        df_dr_numerical = radial_deriv(f_test)
        df_dtheta_numerical = angular_deriv(f_test)
        
        # Check accuracy (excluding boundaries)
        r_error = np.mean(np.abs(df_dr_numerical[1:-1, :] - df_dr_analytical[1:-1, :]))
        theta_error = np.mean(np.abs(df_dtheta_numerical[:, 1:-1] - df_dtheta_analytical[:, 1:-1]))
        
        if r_error < 0.1 and theta_error < 0.1:
            print("✓ Finite difference operators test passed")
            print(f"  Radial derivative error: {r_error:.6f}")
            print(f"  Angular derivative error: {theta_error:.6f}")
        else:
            print(f"✗ Finite difference operators test failed")
            print(f"  Radial derivative error: {r_error:.6f}")
            print(f"  Angular derivative error: {theta_error:.6f}")
            
    except Exception as e:
        print(f"✗ Finite difference test failed: {e}")

def test_conservation_properties(model, regions):
    """Test conservation properties"""
    print("\nTesting conservation properties...")
    
    try:
        # Test mass conservation for electrolyte
        c_e_total_initial = np.sum(model.c_e * model.R * model.dr * model.dtheta)
        
        # Simple diffusion step (no sources)
        equations = DFNEquations(model)
        i_e_zero = np.zeros_like(model.c_e)
        c_e_new = equations.electrolyte_diffusion_2d(
            model.c_e, model.phi_e, i_e_zero, regions, model.dt)
        
        c_e_total_final = np.sum(c_e_new * model.R * model.dr * model.dtheta)
        
        mass_conservation_error = abs(c_e_total_final - c_e_total_initial) / c_e_total_initial
        
        if mass_conservation_error < 0.01:  # 1% tolerance
            print(f"✓ Mass conservation test passed (error: {mass_conservation_error:.6f})")
        else:
            print(f"✗ Mass conservation test failed (error: {mass_conservation_error:.6f})")
            
    except Exception as e:
        print(f"✗ Conservation test failed: {e}")

def run_all_tests():
    """Run all tests"""
    print("="*60)
    print("CYLINDRICAL DFN MODEL TEST SUITE")
    print("="*60)
    
    # Test 1: Model initialization
    model = test_model_initialization()
    if model is None:
        print("\n✗ Cannot proceed with other tests - model initialization failed")
        return
    
    # Test 2: Electrode regions
    regions = test_electrode_regions(model)
    if regions is None:
        print("\n✗ Cannot proceed with region-dependent tests")
        return
    
    # Test 3: Visualization
    fig1, fig2 = test_visualization(model, regions)
    
    # Test 4: Equations
    equations = test_equations(model, regions)
    
    # Test 5: Finite differences
    test_finite_differences()
    
    # Test 6: Conservation
    test_conservation_properties(model, regions)
    
    print("\n" + "="*60)
    print("TEST SUITE COMPLETED")
    print("="*60)
    
    # Show plots if successful
    if fig1 is not None and fig2 is not None:
        plt.show()
    
    return model, regions, equations

if __name__ == "__main__":
    # Run tests
    model, regions, equations = run_all_tests()
    
    # Additional interactive testing
    if model is not None:
        print("\nModel object available as 'model'")
        print("Regions array available as 'regions'")
        print("Equations object available as 'equations'")
        print("\nYou can now interact with the model for further testing.")
