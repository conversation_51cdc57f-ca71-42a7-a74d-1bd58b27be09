"""
Simple simulation example for the cylindrical DFN model

This script demonstrates how to run a basic discharge simulation.
"""

import numpy as np
import matplotlib.pyplot as plt
from dfn_cylindrical import CylindricalDFNModel, create_example_cell
from dfn_visualization import DFNVisualizer
from dfn_equations import DFNEquations

def run_simple_discharge(model, current_applied=1.0, duration=300):
    """
    Run a simple constant current discharge simulation
    
    Parameters:
    -----------
    model : CylindricalDFNModel
        The DFN model instance
    current_applied : float
        Applied current in Amperes (positive for discharge)
    duration : float
        Simulation duration in seconds
    """
    print(f"Running discharge simulation...")
    print(f"Applied current: {current_applied} A")
    print(f"Duration: {duration} s")
    
    # Initialize equations solver
    equations = DFNEquations(model)
    
    # Get electrode regions
    regions = model.get_electrode_regions()
    
    # Time stepping parameters
    dt = model.dt
    n_steps = int(duration / dt)
    
    # Storage for results
    time_points = []
    voltage_history = []
    c_e_history = []
    T_history = []
    
    # Current density distribution (simplified)
    # In reality, this would be calculated from charge conservation
    current_density = current_applied / (np.pi * (model.R_outer**2 - model.R_inner**2))
    
    print(f"Average current density: {current_density:.2f} A/m²")
    
    # Main simulation loop
    for step in range(n_steps):
        t = step * dt
        
        if step % 50 == 0:  # Print progress every 50 steps
            print(f"  Step {step}/{n_steps}, t = {t:.1f} s")
        
        # Apply current (simplified distribution)
        i_e = np.zeros_like(model.c_e)
        for i in range(model.nr):
            for j in range(model.ntheta):
                region = regions[i, j]
                if region == 0:  # Negative electrode
                    i_e[i, j] = current_density
                elif region == 2:  # Positive electrode
                    i_e[i, j] = -current_density
                # Separator has no current generation
        
        # Solve electrolyte diffusion
        model.c_e = equations.electrolyte_diffusion_2d(
            model.c_e, model.phi_e, i_e, regions, dt)
        
        # Simple thermal update (heat generation from overpotential)
        heat_gen = np.abs(i_e) * 0.1  # Simplified heat generation
        model.T += heat_gen * dt / 1000  # Simple thermal update
        
        # Calculate cell voltage (simplified)
        # In reality, this would be from potential difference between electrodes
        avg_concentration = np.mean(model.c_e)
        concentration_factor = avg_concentration / model.electrolyte_params['c_e_init']
        
        # Simple voltage model (decreases with discharge and concentration changes)
        voltage = 3.7 - 0.5 * (t / duration) - 0.2 * (1 - concentration_factor)
        
        # Store results
        if step % 10 == 0:  # Store every 10 steps to save memory
            time_points.append(t)
            voltage_history.append(voltage)
            c_e_history.append(model.c_e.copy())
            T_history.append(model.T.copy())
    
    print("Simulation completed!")
    
    return {
        'time': np.array(time_points),
        'voltage': np.array(voltage_history),
        'concentration': c_e_history,
        'temperature': T_history,
        'regions': regions
    }

def plot_simulation_results(results, model):
    """
    Plot the simulation results
    """
    viz = DFNVisualizer(model)
    
    # Create figure with subplots
    fig = plt.figure(figsize=(15, 12))
    
    # Plot 1: Voltage vs time
    ax1 = plt.subplot(3, 3, 1)
    plt.plot(results['time'] / 60, results['voltage'], 'b-', linewidth=2)
    plt.xlabel('Time (min)')
    plt.ylabel('Voltage (V)')
    plt.title('Discharge Curve')
    plt.grid(True, alpha=0.3)
    
    # Plot 2: Initial concentration distribution
    ax2 = plt.subplot(3, 3, 2)
    R, THETA = model.R, model.THETA
    X = R * np.cos(THETA)
    Y = R * np.sin(THETA)
    im2 = plt.contourf(X, Y, results['concentration'][0], levels=20, cmap='viridis')
    plt.colorbar(im2, ax=ax2, label='c_e (mol/m³)')
    plt.xlabel('X (m)')
    plt.ylabel('Y (m)')
    plt.title('Initial Concentration')
    plt.axis('equal')
    
    # Plot 3: Final concentration distribution
    ax3 = plt.subplot(3, 3, 3)
    im3 = plt.contourf(X, Y, results['concentration'][-1], levels=20, cmap='viridis')
    plt.colorbar(im3, ax=ax3, label='c_e (mol/m³)')
    plt.xlabel('X (m)')
    plt.ylabel('Y (m)')
    plt.title('Final Concentration')
    plt.axis('equal')
    
    # Plot 4: Concentration change
    ax4 = plt.subplot(3, 3, 4)
    conc_change = results['concentration'][-1] - results['concentration'][0]
    im4 = plt.contourf(X, Y, conc_change, levels=20, cmap='RdBu_r')
    plt.colorbar(im4, ax=ax4, label='Δc_e (mol/m³)')
    plt.xlabel('X (m)')
    plt.ylabel('Y (m)')
    plt.title('Concentration Change')
    plt.axis('equal')
    
    # Plot 5: Radial concentration profile evolution
    ax5 = plt.subplot(3, 3, 5)
    r_mm = model.r * 1000
    theta_idx = 0  # At θ = 0
    
    # Plot profiles at different times
    time_indices = [0, len(results['concentration'])//4, 
                   len(results['concentration'])//2, -1]
    colors = ['blue', 'green', 'orange', 'red']
    labels = ['Initial', '25%', '50%', 'Final']
    
    for i, (idx, color, label) in enumerate(zip(time_indices, colors, labels)):
        profile = results['concentration'][idx][:, theta_idx]
        plt.plot(r_mm, profile, color=color, linewidth=2, label=label)
    
    plt.xlabel('Radius (mm)')
    plt.ylabel('Concentration (mol/m³)')
    plt.title('Radial Concentration Profiles')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 6: Angular concentration profile evolution
    ax6 = plt.subplot(3, 3, 6)
    r_idx = len(model.r) // 2  # Middle radius
    theta_deg = model.theta * 180 / np.pi
    
    for i, (idx, color, label) in enumerate(zip(time_indices, colors, labels)):
        profile = results['concentration'][idx][r_idx, :]
        plt.plot(theta_deg, profile, color=color, linewidth=2, label=label)
    
    plt.xlabel('Angle (degrees)')
    plt.ylabel('Concentration (mol/m³)')
    plt.title('Angular Concentration Profiles')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 7: Temperature distribution (final)
    ax7 = plt.subplot(3, 3, 7)
    im7 = plt.contourf(X, Y, results['temperature'][-1], levels=20, cmap='hot')
    plt.colorbar(im7, ax=ax7, label='Temperature (K)')
    plt.xlabel('X (m)')
    plt.ylabel('Y (m)')
    plt.title('Final Temperature')
    plt.axis('equal')
    
    # Plot 8: Electrode regions
    ax8 = plt.subplot(3, 3, 8)
    im8 = plt.contourf(X, Y, results['regions'], levels=[0, 0.5, 1.5, 2.5], 
                      colors=['blue', 'gray', 'red'], alpha=0.7)
    plt.xlabel('X (m)')
    plt.ylabel('Y (m)')
    plt.title('Electrode Regions')
    plt.axis('equal')
    
    # Add legend for regions
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor='blue', label='Negative'),
                      Patch(facecolor='gray', label='Separator'),
                      Patch(facecolor='red', label='Positive')]
    plt.legend(handles=legend_elements, loc='upper right')
    
    # Plot 9: Average concentration vs time
    ax9 = plt.subplot(3, 3, 9)
    avg_conc = [np.mean(c) for c in results['concentration']]
    plt.plot(results['time'] / 60, avg_conc, 'g-', linewidth=2)
    plt.xlabel('Time (min)')
    plt.ylabel('Average Concentration (mol/m³)')
    plt.title('Average Concentration vs Time')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig

def main():
    """
    Main function to run the simulation example
    """
    print("="*60)
    print("CYLINDRICAL DFN MODEL - SIMPLE DISCHARGE SIMULATION")
    print("="*60)
    
    # Create model
    cell_params, material_params, numerical_params = create_example_cell()
    
    # Reduce time step for stability
    numerical_params['dt'] = 0.5
    
    model = CylindricalDFNModel(cell_params, material_params, numerical_params)
    
    # Run simulation
    current = 2.0  # 2A discharge current
    duration = 300  # 5 minutes
    
    results = run_simple_discharge(model, current, duration)
    
    # Plot results
    fig = plot_simulation_results(results, model)
    
    # Print summary
    print("\n" + "="*60)
    print("SIMULATION SUMMARY")
    print("="*60)
    print(f"Initial voltage: {results['voltage'][0]:.3f} V")
    print(f"Final voltage: {results['voltage'][-1]:.3f} V")
    print(f"Voltage drop: {results['voltage'][0] - results['voltage'][-1]:.3f} V")
    
    initial_conc = np.mean(results['concentration'][0])
    final_conc = np.mean(results['concentration'][-1])
    print(f"Initial avg concentration: {initial_conc:.1f} mol/m³")
    print(f"Final avg concentration: {final_conc:.1f} mol/m³")
    print(f"Concentration change: {final_conc - initial_conc:.1f} mol/m³")
    
    initial_temp = np.mean(results['temperature'][0])
    final_temp = np.mean(results['temperature'][-1])
    print(f"Initial avg temperature: {initial_temp:.1f} K")
    print(f"Final avg temperature: {final_temp:.1f} K")
    print(f"Temperature rise: {final_temp - initial_temp:.1f} K")
    
    plt.show()
    
    return model, results

if __name__ == "__main__":
    model, results = main()
