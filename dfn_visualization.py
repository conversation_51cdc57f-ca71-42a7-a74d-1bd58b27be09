"""
Visualization Module for Cylindrical DFN Model

This module provides visualization tools for the 2D cylindrical DFN model results.
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as patches

class DFNVisualizer:
    """
    Visualization tools for cylindrical DFN model
    """
    
    def __init__(self, model):
        """
        Initialize visualizer with model reference
        
        Parameters:
        -----------
        model : CylindricalDFNModel
            Reference to the DFN model
        """
        self.model = model
        
    def plot_cell_geometry(self, regions=None):
        """
        Plot the cylindrical cell geometry and electrode regions
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Plot 1: Cross-section view (r-θ)
        R, THETA = self.model.R, self.model.THETA
        X = R * np.cos(THETA)
        Y = R * np.sin(THETA)
        
        if regions is not None:
            # Color-code regions
            colors = ['blue', 'gray', 'red']  # neg, sep, pos
            region_colors = np.array(colors)[regions]
            
            ax1.scatter(<PERSON>.flatten(), Y.flatten(), c=regions.flatten(), 
                       cmap='viridis', s=1, alpha=0.7)
            ax1.set_title('Electrode Regions (Top View)')
        else:
            ax1.scatter(X.flatten(), Y.flatten(), c='lightblue', s=1)
            ax1.set_title('Cell Geometry (Top View)')
            
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_aspect('equal')
        ax1.grid(True, alpha=0.3)
        
        # Add circles for inner and outer radius
        circle_inner = plt.Circle((0, 0), self.model.R_inner, 
                                 fill=False, color='black', linewidth=2)
        circle_outer = plt.Circle((0, 0), self.model.R_outer, 
                                 fill=False, color='black', linewidth=2)
        ax1.add_patch(circle_inner)
        ax1.add_patch(circle_outer)
        
        # Plot 2: Side view (cylindrical)
        ax2.add_patch(patches.Rectangle((0, 0), self.model.height, 
                                       2*self.model.R_outer, 
                                       fill=False, edgecolor='black', linewidth=2))
        ax2.add_patch(patches.Rectangle((0, self.model.R_inner), self.model.height, 
                                       2*(self.model.R_outer - self.model.R_inner), 
                                       fill=True, facecolor='lightblue', alpha=0.3))
        
        ax2.set_xlim(-0.01, self.model.height + 0.01)
        ax2.set_ylim(-0.01, 2*self.model.R_outer + 0.01)
        ax2.set_xlabel('Height (m)')
        ax2.set_ylabel('Diameter (m)')
        ax2.set_title('Cell Side View')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
        
    def plot_concentration_2d(self, c_e, title="Electrolyte Concentration"):
        """
        Plot 2D concentration distribution in polar coordinates
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Plot 1: Polar plot
        R, THETA = self.model.R, self.model.THETA
        
        im1 = ax1.contourf(THETA, R, c_e, levels=20, cmap='viridis')
        ax1.set_xlabel('θ (rad)')
        ax1.set_ylabel('r (m)')
        ax1.set_title(f'{title} (r-θ coordinates)')
        plt.colorbar(im1, ax=ax1, label='Concentration (mol/m³)')
        
        # Plot 2: Cartesian projection
        X = R * np.cos(THETA)
        Y = R * np.sin(THETA)
        
        im2 = ax2.contourf(X, Y, c_e, levels=20, cmap='viridis')
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Y (m)')
        ax2.set_title(f'{title} (Cartesian projection)')
        ax2.set_aspect('equal')
        plt.colorbar(im2, ax=ax2, label='Concentration (mol/m³)')
        
        plt.tight_layout()
        return fig
        
    def plot_potential_2d(self, phi, title="Potential"):
        """
        Plot 2D potential distribution
        """
        return self.plot_concentration_2d(phi, title)
        
    def plot_current_density_2d(self, i_e, title="Current Density"):
        """
        Plot 2D current density distribution
        """
        return self.plot_concentration_2d(i_e, title)
        
    def plot_temperature_2d(self, T, title="Temperature"):
        """
        Plot 2D temperature distribution
        """
        return self.plot_concentration_2d(T, title)
        
    def plot_radial_profiles(self, variables_dict, theta_index=0):
        """
        Plot radial profiles of variables at a specific angular position
        
        Parameters:
        -----------
        variables_dict : dict
            Dictionary with variable names as keys and 2D arrays as values
        theta_index : int
            Angular index to extract radial profile
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()
        
        r = self.model.r
        
        for i, (var_name, var_data) in enumerate(variables_dict.items()):
            if i >= 4:  # Only plot first 4 variables
                break
                
            ax = axes[i]
            profile = var_data[:, theta_index]
            ax.plot(r * 1000, profile, 'b-', linewidth=2)
            ax.set_xlabel('Radius (mm)')
            ax.set_ylabel(var_name)
            ax.set_title(f'{var_name} vs Radius (θ = {self.model.theta[theta_index]:.2f} rad)')
            ax.grid(True, alpha=0.3)
            
        plt.tight_layout()
        return fig
        
    def plot_angular_profiles(self, variables_dict, r_index=None):
        """
        Plot angular profiles of variables at a specific radial position
        
        Parameters:
        -----------
        variables_dict : dict
            Dictionary with variable names as keys and 2D arrays as values
        r_index : int
            Radial index to extract angular profile (default: middle)
        """
        if r_index is None:
            r_index = len(self.model.r) // 2
            
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()
        
        theta_deg = self.model.theta * 180 / np.pi
        
        for i, (var_name, var_data) in enumerate(variables_dict.items()):
            if i >= 4:  # Only plot first 4 variables
                break
                
            ax = axes[i]
            profile = var_data[r_index, :]
            ax.plot(theta_deg, profile, 'r-', linewidth=2)
            ax.set_xlabel('Angle (degrees)')
            ax.set_ylabel(var_name)
            ax.set_title(f'{var_name} vs Angle (r = {self.model.r[r_index]*1000:.1f} mm)')
            ax.grid(True, alpha=0.3)
            
        plt.tight_layout()
        return fig
        
    def plot_time_evolution(self, time_data, variable_name, r_index=None, theta_index=0):
        """
        Plot time evolution of a variable at a specific location
        
        Parameters:
        -----------
        time_data : list
            List of 2D arrays for each time step
        variable_name : str
            Name of the variable for labeling
        r_index : int
            Radial index (default: middle)
        theta_index : int
            Angular index (default: 0)
        """
        if r_index is None:
            r_index = len(self.model.r) // 2
            
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Extract time series at specific location
        time_series = [data[r_index, theta_index] for data in time_data]
        time_points = np.arange(len(time_series)) * self.model.dt
        
        ax.plot(time_points, time_series, 'b-', linewidth=2)
        ax.set_xlabel('Time (s)')
        ax.set_ylabel(variable_name)
        ax.set_title(f'{variable_name} vs Time at r={self.model.r[r_index]*1000:.1f}mm, θ={self.model.theta[theta_index]:.2f}rad')
        ax.grid(True, alpha=0.3)
        
        return fig
        
    def create_animation_frames(self, time_data, variable_name, save_frames=False):
        """
        Create animation frames for time evolution
        
        Parameters:
        -----------
        time_data : list
            List of 2D arrays for each time step
        variable_name : str
            Name of the variable
        save_frames : bool
            Whether to save individual frames
        """
        frames = []
        
        for i, data in enumerate(time_data):
            fig = self.plot_concentration_2d(data, f'{variable_name} at t={i*self.model.dt:.1f}s')
            
            if save_frames:
                fig.savefig(f'frame_{i:04d}.png', dpi=100, bbox_inches='tight')
                
            frames.append(fig)
            
        return frames
        
    def plot_discharge_curve(self, voltage_data, current_data, time_data):
        """
        Plot discharge curve (voltage vs capacity)
        
        Parameters:
        -----------
        voltage_data : array
            Cell voltage over time
        current_data : array
            Applied current over time
        time_data : array
            Time points
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
        
        # Calculate capacity
        capacity = np.cumsum(current_data * np.diff(time_data, prepend=0)) / 3600  # Ah
        
        # Voltage vs time
        ax1.plot(time_data / 3600, voltage_data, 'b-', linewidth=2)
        ax1.set_xlabel('Time (h)')
        ax1.set_ylabel('Voltage (V)')
        ax1.set_title('Voltage vs Time')
        ax1.grid(True, alpha=0.3)
        
        # Voltage vs capacity
        ax2.plot(capacity, voltage_data, 'r-', linewidth=2)
        ax2.set_xlabel('Capacity (Ah)')
        ax2.set_ylabel('Voltage (V)')
        ax2.set_title('Discharge Curve')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig

def create_example_visualization():
    """
    Create example visualization with dummy data
    """
    from dfn_cylindrical import create_example_cell, CylindricalDFNModel
    
    # Create model
    cell_params, material_params, numerical_params = create_example_cell()
    model = CylindricalDFNModel(cell_params, material_params, numerical_params)
    
    # Create visualizer
    viz = DFNVisualizer(model)
    
    # Get electrode regions
    regions = model.get_electrode_regions()
    
    # Plot geometry
    fig1 = viz.plot_cell_geometry(regions)
    
    # Create dummy concentration data
    R, THETA = model.R, model.THETA
    c_e_dummy = 1000 + 100 * np.sin(2*THETA) * np.exp(-(R - model.R_inner)**2 / 0.001)
    
    # Plot concentration
    fig2 = viz.plot_concentration_2d(c_e_dummy)
    
    return fig1, fig2

if __name__ == "__main__":
    # Example usage
    fig1, fig2 = create_example_visualization()
    plt.show()
