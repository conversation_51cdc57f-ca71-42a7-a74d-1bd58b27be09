"""
DFN Equations Module for Cylindrical Geometry

This module contains the core DFN equations implemented for cylindrical coordinates (r, θ).
The equations include:
1. Electrolyte diffusion and migration
2. Solid-phase diffusion in particles
3. Butler-Volmer kinetics
4. Charge conservation
5. Thermal effects
"""

import numpy as np
from scipy.sparse import diags, csc_matrix
from scipy.sparse.linalg import spsolve

class DFNEquations:
    """
    Core DFN equations for cylindrical geometry
    """
    
    def __init__(self, model):
        """
        Initialize with reference to the main model
        
        Parameters:
        -----------
        model : CylindricalDFNModel
            Reference to the main model object
        """
        self.model = model
        self.F = 96485.0        # Faraday constant (C/mol)
        self.R_gas = 8.314      # Gas constant (J/mol/K)
        
    def electrolyte_diffusion_2d(self, c_e, phi_e, i_e, regions, dt):
        """
        Solve electrolyte diffusion equation in cylindrical coordinates
        
        ∂c_e/∂t = ∇·(D_e ∇c_e) + (1-t_+)/F ∇·i_e
        
        In cylindrical coordinates:
        ∇²c_e = (1/r)(∂/∂r)(r ∂c_e/∂r) + (1/r²)(∂²c_e/∂θ²)
        """
        nr, ntheta = c_e.shape
        dr = self.model.dr
        dtheta = self.model.dtheta
        r = self.model.r
        
        # Create coefficient matrices for implicit scheme
        # dc_e/dt = A * c_e + source_term
        
        # Initialize sparse matrix components
        row_indices = []
        col_indices = []
        data = []
        rhs = np.zeros_like(c_e)
        
        for i in range(nr):
            for j in range(ntheta):
                idx = i * ntheta + j
                
                # Get local diffusivity based on region
                region = regions[i, j]
                if region == 0:  # Negative electrode
                    D_e_eff = (self.model.electrolyte_params['D_e'] * 
                              self.model.neg_params['epsilon_e']**1.5)
                elif region == 1:  # Separator
                    D_e_eff = (self.model.electrolyte_params['D_e'] * 
                              self.model.sep_params['epsilon_e']**1.5)
                else:  # Positive electrode
                    D_e_eff = (self.model.electrolyte_params['D_e'] * 
                              self.model.pos_params['epsilon_e']**1.5)
                
                # Central difference for radial direction
                if i == 0:  # Inner boundary
                    # Neumann BC: ∂c_e/∂r = 0
                    row_indices.append(idx)
                    col_indices.append(idx)
                    data.append(1.0)
                    
                    row_indices.append(idx)
                    col_indices.append((i+1) * ntheta + j)
                    data.append(-1.0)
                    
                    rhs[i, j] = 0.0
                    
                elif i == nr - 1:  # Outer boundary
                    # Neumann BC: ∂c_e/∂r = 0
                    row_indices.append(idx)
                    col_indices.append(idx)
                    data.append(1.0)
                    
                    row_indices.append(idx)
                    col_indices.append((i-1) * ntheta + j)
                    data.append(-1.0)
                    
                    rhs[i, j] = 0.0
                    
                else:  # Interior points
                    # Radial diffusion term: (1/r)(∂/∂r)(r ∂c_e/∂r)
                    r_i = r[i]
                    
                    # Coefficients for finite difference
                    a_r = D_e_eff * dt / (dr**2)
                    b_r = D_e_eff * dt / (2 * r_i * dr)
                    
                    # Angular diffusion term: (1/r²)(∂²c_e/∂θ²)
                    a_theta = D_e_eff * dt / (r_i**2 * dtheta**2)
                    
                    # Main diagonal
                    row_indices.append(idx)
                    col_indices.append(idx)
                    data.append(1.0 + 2*a_r + 2*a_theta)
                    
                    # Radial neighbors
                    row_indices.append(idx)
                    col_indices.append((i-1) * ntheta + j)
                    data.append(-(a_r - b_r))
                    
                    row_indices.append(idx)
                    col_indices.append((i+1) * ntheta + j)
                    data.append(-(a_r + b_r))
                    
                    # Angular neighbors (periodic boundary conditions)
                    j_prev = (j - 1) % ntheta
                    j_next = (j + 1) % ntheta
                    
                    row_indices.append(idx)
                    col_indices.append(i * ntheta + j_prev)
                    data.append(-a_theta)
                    
                    row_indices.append(idx)
                    col_indices.append(i * ntheta + j_next)
                    data.append(-a_theta)
                    
                    # Source term from current
                    if region != 1:  # Not in separator
                        t_plus = self.model.electrolyte_params['t_plus']
                        source = (1 - t_plus) / self.F * i_e[i, j] * dt
                        rhs[i, j] = c_e[i, j] + source
                    else:
                        rhs[i, j] = c_e[i, j]
        
        # Solve the linear system
        A = csc_matrix((data, (row_indices, col_indices)), 
                       shape=(nr*ntheta, nr*ntheta))
        c_e_flat = spsolve(A, rhs.flatten())
        
        return c_e_flat.reshape((nr, ntheta))
    
    def solid_diffusion_spherical(self, c_s_surf, electrode_params, dt):
        """
        Solve solid-phase diffusion in spherical particles
        
        This is a simplified approach using the surface concentration
        and assuming a parabolic profile in the particle
        """
        # Simplified solid diffusion using surface flux balance
        # More sophisticated implementation would solve the full spherical diffusion
        
        D_s = electrode_params['D_s']
        R_s = electrode_params['R_s']
        
        # Diffusion time constant
        tau = R_s**2 / (15 * D_s)
        
        # Simple exponential approach to equilibrium
        # This is a placeholder - full implementation would use proper spherical coordinates
        return c_s_surf  # Placeholder
    
    def butler_volmer_kinetics(self, c_e, c_s_surf, phi_s, phi_e, T, electrode_params):
        """
        Calculate exchange current density and overpotential using Butler-Volmer kinetics
        
        i = i_0 * [exp(α_a*F*η/RT) - exp(-α_c*F*η/RT)]
        
        where η = phi_s - phi_e - U_eq
        """
        # Exchange current density
        k_0 = 1e-11  # Reaction rate constant (m/s) - typical value
        c_s_max = electrode_params['c_s_max']
        
        # Concentration-dependent exchange current
        i_0 = (k_0 * self.F * 
               np.sqrt(c_e * c_s_surf * (c_s_max - c_s_surf)))
        
        # Equilibrium potential (simplified - should be function of SOC)
        if 'neg' in str(electrode_params):
            U_eq = 0.1  # V vs Li/Li+ (graphite approximation)
        else:
            U_eq = 4.0  # V vs Li/Li+ (LiCoO2 approximation)
        
        # Overpotential
        eta = phi_s - phi_e - U_eq
        
        # Butler-Volmer equation
        alpha_a = 0.5  # Anodic transfer coefficient
        alpha_c = 0.5  # Cathodic transfer coefficient
        
        exp_term_a = np.exp(alpha_a * self.F * eta / (self.R_gas * T))
        exp_term_c = np.exp(-alpha_c * self.F * eta / (self.R_gas * T))
        
        # Current density
        i = i_0 * (exp_term_a - exp_term_c)
        
        return i, i_0, eta
    
    def charge_conservation_electrolyte(self, phi_e, c_e, i_e, regions):
        """
        Solve charge conservation in electrolyte phase
        
        ∇·i_e = 0
        i_e = -κ_eff ∇φ_e + (2κ_eff RT/F)(1-t_+)(∇ln(c_e))
        """
        nr, ntheta = phi_e.shape
        dr = self.model.dr
        dtheta = self.model.dtheta
        r = self.model.r
        
        # This is a simplified implementation
        # Full implementation would solve the coupled system
        
        return phi_e  # Placeholder
    
    def charge_conservation_solid(self, phi_s, i_s, regions, electrode_type):
        """
        Solve charge conservation in solid phase
        
        ∇·i_s = 0
        i_s = -σ_eff ∇φ_s
        """
        # Simplified implementation
        return phi_s  # Placeholder
    
    def thermal_equation(self, T, heat_generation, dt):
        """
        Solve thermal equation
        
        ρCp ∂T/∂t = ∇·(k_thermal ∇T) + Q_gen
        """
        # Simplified thermal model
        # Full implementation would include proper thermal properties
        
        return T  # Placeholder

def create_finite_difference_operators_2d(nr, ntheta, dr, dtheta, r):
    """
    Create finite difference operators for cylindrical coordinates
    
    Returns operators for:
    - Radial derivatives
    - Angular derivatives  
    - Laplacian in cylindrical coordinates
    """
    
    # Radial derivative operator
    def radial_derivative(f):
        df_dr = np.zeros_like(f)
        
        # Central differences for interior points
        df_dr[1:-1, :] = (f[2:, :] - f[:-2, :]) / (2 * dr)
        
        # Boundary conditions
        df_dr[0, :] = (f[1, :] - f[0, :]) / dr      # Forward difference
        df_dr[-1, :] = (f[-1, :] - f[-2, :]) / dr   # Backward difference
        
        return df_dr
    
    # Angular derivative operator
    def angular_derivative(f):
        df_dtheta = np.zeros_like(f)
        
        # Central differences with periodic boundary conditions
        df_dtheta[:, 1:-1] = (f[:, 2:] - f[:, :-2]) / (2 * dtheta)
        df_dtheta[:, 0] = (f[:, 1] - f[:, -1]) / (2 * dtheta)
        df_dtheta[:, -1] = (f[:, 0] - f[:, -2]) / (2 * dtheta)
        
        return df_dtheta
    
    # Laplacian operator in cylindrical coordinates
    def laplacian_cylindrical(f):
        # ∇²f = (1/r)(∂/∂r)(r ∂f/∂r) + (1/r²)(∂²f/∂θ²)
        
        # Radial part: (1/r)(∂/∂r)(r ∂f/∂r)
        df_dr = radial_derivative(f)
        
        radial_part = np.zeros_like(f)
        for i in range(nr):
            if i == 0:
                # L'Hôpital's rule at r=0: (1/r)(∂/∂r)(r ∂f/∂r) → 2(∂²f/∂r²)
                radial_part[i, :] = 2 * (f[1, :] - f[0, :]) / dr**2
            else:
                r_i = r[i]
                # Second derivative
                d2f_dr2 = (f[i+1, :] - 2*f[i, :] + f[i-1, :]) / dr**2 if i < nr-1 else 0
                radial_part[i, :] = (1/r_i) * df_dr[i, :] + d2f_dr2
        
        # Angular part: (1/r²)(∂²f/∂θ²)
        angular_part = np.zeros_like(f)
        for i in range(nr):
            r_i = r[i] if i > 0 else dr/2  # Avoid division by zero
            # Second derivative in theta with periodic BC
            d2f_dtheta2 = np.zeros(ntheta)
            d2f_dtheta2[1:-1] = (f[i, 2:] - 2*f[i, 1:-1] + f[i, :-2]) / dtheta**2
            d2f_dtheta2[0] = (f[i, 1] - 2*f[i, 0] + f[i, -1]) / dtheta**2
            d2f_dtheta2[-1] = (f[i, 0] - 2*f[i, -1] + f[i, -2]) / dtheta**2
            
            angular_part[i, :] = d2f_dtheta2 / r_i**2
        
        return radial_part + angular_part
    
    return radial_derivative, angular_derivative, laplacian_cylindrical
